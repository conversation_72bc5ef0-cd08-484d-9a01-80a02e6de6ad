from PIL import Image
import sys

# The list of characters to use, in order from darkest to lightest.
# (You can reverse the order if you prefer a different mapping.)
ASCII_CHARS = [
    "0", "1", "2", "3", "4", "5", "6", "7", "8", "9",
    "+", "=", "@", "%", "^", "*", "-", "/", "<", ">"
]

def resize_image(image, new_width=78):
    """Resizes the image preserving the aspect ratio."""
    width, height = image.size
    aspect_ratio = height / width
    new_height = int(new_width * aspect_ratio)
    return image.resize((new_width, new_height))

def grayify(image):
    """Converts the image to grayscale."""
    return image.convert("L")

def pixels_to_ascii(image):
    """Maps each pixel to an ASCII character from the specified set."""
    pixels = image.getdata()
    ascii_str = ""
    # Map each pixel (0-255) to a character index (0 to len(ASCII_CHARS)-1)
    for pixel in pixels:
        index = pixel * (len(ASCII_CHARS) - 1) // 255
        ascii_str += ASCII_CHARS[index]
    return ascii_str

def main(new_width=100):
    # Get the image path from command line arguments; default to 'input.png'
    path = sys.argv[1] if len(sys.argv) > 1 else "input.png"
    try:
        image = Image.open(path)
    except Exception as e:
        print(f"Unable to open image file '{path}'.")
        print(e)
        return

    # Process the image: resize, convert to grayscale, and map pixels to ASCII.
    image = resize_image(image, new_width)
    image = grayify(image)
    ascii_str = pixels_to_ascii(image)

    # Format the string based on image width.
    img_width = image.width
    ascii_img = ""
    for i in range(0, len(ascii_str), img_width):
        ascii_img += ascii_str[i:i+img_width] + "\n"

    # Output the ASCII art.
    print(ascii_img)

    # Optionally, save the ASCII art to a file.
    with open("output_ascii.txt", "w") as f:
        f.write(ascii_img)

if __name__ == '__main__':
    main()
