{% extends "base.html" %}

{% block title %}News &ndash; GPU MODE{% endblock %}

{% block content %}

<div class="content-stack">
<h1>News and Announcements</h1>

<div class="news-item" id="triangle-multiplication">
{% set color = 'Triangle Multiplicative Update'|to_color %}
<h2><div class="{{ color }}-square"></div>
New competition problem: Triangle Multiplicative Update (June 24, 2025)</h2>

<p>
We are excited to announce a really exciting and challenging GPU competition problem on the GPU MODE leaderboard that you can optimize across NVIDIA (A100, H100, B200) and AMD hardware (MI300)! This is our first problem across multiple hardware vendors, and is also particularly challenging (<a href="https://x.com/a1zhang">@a1zhang</a> has been particularly excited about it for months). You will be optimizing the <b>Triangle Multiplicative Update kernel</b> from the landmark <a href="https://deepmind.google/science/alphafold/">AlphaFold2/3</a> line of work that won a Nobel Prize! Here is the <b>official problem writeup: <a href="https://tinyurl.com/gpumode-trimul">https://tinyurl.com/gpumode-trimul</a></b>
</p>

<p>
Funnily enough, while writing up this problem, we noticed that NVIDIA actually released that they had been working on this kernel in their cuEquivariance library — what a coincidence! We ask that participants do not use this library in their solutions, as it is closed source (we will be automatically and manually removing these solutions on the leaderboard). Honestly though, I’m confident that you all can easily beat their solution!
</p>

<center>
<img src="{{ url_for('static', filename='images/trimul_teaser.png') }}"
alt="New competition problem: Triangle Multiplicative Update" style="width: 50% !important; height: 50% !important; max-width: 50% !important;">
</center>

<p>
<b>Why a BioML kernel?</b> A lot of the kernels / problems you will see in these GPU MODE kernel writing competitions center around large language model training — our last competition, for example, centered around popular single-device kernels used in DeepSeek-V3/R1. Many future problems may also revolve around communication kernels where we provide a whole node such as the expert parallelism MoE.
</p>

<center>
<img src="{{ url_for('static', filename='images/google.png') }}"
alt="" style="width: 50% !important; height: 50% !important; max-width: 50% !important; display: block !important; margin: 0 auto !important;">
</center>

<p>
However, many of these kernels we have in mind have already been heavily optimized by experts in labs like DeepSeek, OpenAI, Anthropic, Google DeepMind, etc. so we wanted to design some problems that were still interesting, but had real use cases when solved. The first problem that immediately came to mind was the Triangle Multiplicative Update used in AlphaFold2 and AlphaFold3, a series of extremely influential works in the BioML space that led to <a href="https://www.nobelprize.org/prizes/chemistry/2024/press-release/">John Jumper and Demis Hassabis winning the Nobel Prize in Chemistry</a>. 
This operator is particularly nasty due to its cubic O(n^3) operations. The peak memory of this operator is so bad that most implementations of AlphaFold3 (see <a href="https://github.com/Ligo-Biosciences/AlphaFold3">Ligo’s OSS reproduction</a> and MIT’s <a href="https://github.com/jwohlwend/boltz">Boltz-2</a>) keep the batch size during training at 1, despite the models being less than 1B in parameter size!
</p>

<p>
<b>Note:</b> There will be no huge cash prizes from big company sponsors for this competition, but we will be providing never-before-seen merch for winners for free!
</p>

<p>
Good luck to everyone! 
</p>

</div>

<div class="news-item" id="lisa">
{% set color = 'AMD Competition Results'|to_color %}
<h2><div class="{{ color }}-square"></div>
AMD Competition Success: 30K+ Submissions and Recognition at Advancing AI (June 12, 2025)</h2>

<p>
We are thrilled to share that GPU MODE was recognized on stage by Dr Lisa Su at the <a href="https://www.amd.com/en/corporate/events/advancing-ai.html">Advancing AI</a> closing ceremony, where she said "I wanted to thank the GPU MODE team formed by talented developers from Meta, Hugging Face and MIT, they have been great partners throughout and we could not have done this without them." Back when GPU MODE was just a humble reading group, we never imagined we would be recognized on stage by one of the greatest CEO's of our time.
</p>

<img src="{{ url_for('static', filename='images/lisa.jpeg') }}"
alt="Lisa Su recognizing GPU MODE at Advancing AI" class="max-w-[50%] h-auto">
<p class="text-sm text-gray-600 italic">We were missing the giga cracked Erik (ngc92)</p>


<p>
Our team built the infrastructure for the  <a href="https://www.datamonsters.com/amd-developer-challenge-2025">AMD $100K kernel competition</a>, which ran for 2 months and saw remarkable participation: over 30,000 submissions from 163+ teams. This volume exceeds the total number of kernels collected in <a href="https://huggingface.co/datasets/GPUMODE/KernelBook">KernelBook</a> from crawling all of Github and this represents a significant milestone in aggregating higher quality kernel data </p>

<p>
The results have been outstanding - the best competition kernels are faster than AMD's AITER baselines, all implemented in single files. It was an absolute pleasure meeting some of the top teams in person including Seb, hatoo, Snektron and the grand prize winners ColorsWind.
</p>

<p>
You can see the full results <a href="https://www.gpumode.com/">here</a>.
</p>

<p>
Several top competitors have generously shared their techniques:
</p>

<ul>
    <li><a href="https://github.com/ColorsWind/AMD-Developer-Challenge-2025">ColorsWind's Grand Prize Winning Kernel</a></li>
    <li><a href="https://github.com/luongthecong123/fp8-quant-matmul">Luong The Cong's FP8 Quant MatMul</a></li>
    <li><a href="https://seb-v.github.io/optimization/update/2025/01/20/Fast-GPU-Matrix-multiplication.html">Seb V's Fast GPU Matrix Implementation</a></li>
    <li><a href="https://akashkarnatak.github.io/amd-challenge/">Akash Karnatak's Challenge Solutions</a></li>
    <li><a href="https://www.bilibili.com/read/cv41954307/?opus_fallback=1">Fan Wenjie Technical Analysis</a></li>
    <li><a href="https://github.com/Snektron/gpumode-amd-fp8-mm">Snektron's FP8 Matrix Multiplication</a></li>
</ul>

<p>
We're planning to release all submissions as a permissively licensed dataset, with each solution representing unique tradeoffs between usability and performance. We're working closely with ROCm engineers to upstream the best kernels to PyTorch, leveraging its position as the premier distribution vehicle for kernels.
</p>

<p>
In exciting academic news, our KernelBot platform has been accepted to the ICML CodeML workshop with two strong accepts! Reviewer #2 highlighted the virtuous loop we created: "The paper presents KernelBot, a platform for hosting code optimization competitions, specifically for GPU kernels. Users can submit their implementations and let the system rank them. This serves to (i) educate users how to write efficient GPU kernels, (ii) improve the efficiency of existing GPU kernels, and (iii) collect high quality data for GPU programs that can be used to train generative models."
</p>

<p>
A big thank you to everyone who was involved in <a href="https://gpu-mode.github.io/popcorn/">Popcorn</a> for inspiration, <a href="https://discord.gg/gpumode">discord.gg/gpumode</a> community and of course our amazing collaborators at AMD for making this possible.
</p>

</div>

<div class="news-item" id="amd-100K-challenge">
{% set color = 'AMD Developer Challenge 2025'|to_color %}
<h2><div class="{{ color }}-square"></div>
AMD Developer Challenge 2025: Inference Sprint (April 15, 2025)</h2>

<p>
We are excited to announce the $100K competition hosted by
<a href="https://x.com/AMD">@AMD</a> as part of the first round of our GPU
kernel leaderboard! The theme is writing LLM inference kernels on AMD MI300s,
provided for free through the
<a href="https://www.discord.gg/gpumode">GPU MODE Discord</a>.
</p>

<p>
You (and anyone around the world) can participate for FREE by signing up
<a href="https://www.datamonsters.com/amd-developer-challenge-2025#wf-form-AMD-Email-Form">here</a>.
The format consists of three kernels that are core to DeepSeek's LLM inference:
FP8 GEMM, Multi-Head Latent Attention, and Fused MOE!
</p>

<p>
Competitors will target the AMD MI300 using Triton, but other DSLs and languages
that target AMD hardware are permitted! Participants can form teams of three
when competing, and prizes will be awarded based on team rankings averaged over
the three kernels.
</p>

<p>
Special thanks to <a href="https://x.com/AMD">@AMD</a>,
<a href="https://x.com/indianspeedster">@indianspeedster</a>, and the
amazing GPU MODE Project Popcorn core devs,
<a href="https://x.com/a1zhang">@a1zhang</a>,
<a href="https://x.com/m_sirovatka">@m_sirovatka</a>,
<a href="https://x.com/marksaroufim">@marksaroufim</a>, ngc92 (Erik S.), and
<a href="https://github.com/b9r5">@b9r5</a> for making this competition
possible.
</p>

<p>
We're very excited to accelerate AI research by building on kernels, and we're
super grateful for all of the support from the community!
</p>

<p>
If you're interested in collaborating with us on future competitions / kernels
you think are important, definitely reach out to any one of us on the Popcorn
team!
</p>

<p>
Get started now! The first kernel is the <a href="{{ url_for('static', filename='pdfs/2025-amd-dev-fp8-gemm.pdf') }}" target="_blank">FP8 Groupwise GEMM</a>.
</p>

<img src="{{ url_for('static', filename='images/2025-amd-dev-challenge.jpeg') }}"
alt="2025 AMD Developer Challenge: Inference Sprint" class="max-w-full h-auto">

</div>
</div>

{% endblock %}

