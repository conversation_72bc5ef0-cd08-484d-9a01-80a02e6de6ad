/**
 * Generate a color class name based on a string input.
 * This mimics the Jinja2 to_color filter functionality.
 */
export const toColor = (name: string): string => {
  // Simple hash function to convert string to a consistent color
  let hash = 0;
  for (let i = 0; i < name.length; i++) {
    hash = name.charCodeAt(i) + ((hash << 5) - hash);
    hash = hash & hash; // Convert to 32-bit integer
  }
  
  // Map to color names (matching the CSS classes used in the original)
  const colors = ['red', 'blue', 'green', 'yellow', 'purple', 'orange', 'pink', 'cyan'];
  const colorIndex = Math.abs(hash) % colors.length;
  return colors[colorIndex];
};

/**
 * Get a gradient background for a given name
 * Used for consistent coloring across components
 */
export const getGradientForName = (name: string): string => {
  const gradients = [
    "linear-gradient(90deg, #f6d365 0%, #fda085 100%)", // warm peach
    "linear-gradient(90deg,rgb(134, 179, 250) 0%,rgb(140, 211, 244) 100%)", // calm blue
    "linear-gradient(90deg, #fbc2eb 0%,rgb(252, 154, 216) 100%)", // pink to lavender
    "linear-gradient(90deg,rgb(176, 208, 101) 0%,rgb(119, 176, 127) 100%)", // green mint
    "linear-gradient(90deg, #e0c3fc 0%, #8ec5fc 100%)", // soft purple to blue
    "linear-gradient(90deg, #ff9a9e 0%, #fecfef 100%)", // pink gradient
    "linear-gradient(90deg, #a8edea 0%, #fed6e3 100%)", // mint to pink
    "linear-gradient(90deg, #ffecd2 0%, #fcb69f 100%)", // peach gradient
  ];
  
  let hash = 0;
  for (let i = 0; i < name.length; i++) {
    hash = name.charCodeAt(i) + ((hash << 5) - hash);
    hash = hash & hash;
  }
  
  const gradientIndex = Math.abs(hash) % gradients.length;
  return gradients[gradientIndex];
};

/**
 * Get a color square style for a leaderboard name
 */
export const getColorSquareStyle = (name: string) => {
  const gradient = getGradientForName(name);
  return {
    width: '16px',
    height: '16px',
    borderRadius: '2px',
    background: gradient,
    display: 'inline-block',
    marginRight: '8px',
  };
};
