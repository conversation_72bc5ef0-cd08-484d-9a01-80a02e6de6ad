import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";

dayjs.extend(utc);

/**
 * Calculate time left until deadline.
 * Returns formatted string if deadline is in the future, otherwise "ended".
 * Matches the Python to_time_left function.
 */
export const toTimeLeft = (deadline: string): string => {
  const now = dayjs().utc();
  const deadlineDate = dayjs(deadline);
  
  if (deadlineDate.isBefore(now) || deadlineDate.isSame(now)) {
    return "ended";
  }
  
  const diff = deadlineDate.diff(now);
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  
  const dayLabel = days === 1 ? "day" : "days";
  const hourLabel = hours === 1 ? "hour" : "hours";
  
  return `${days} ${dayLabel} ${hours} ${hourLabel} remaining`;
};

/**
 * Format datetime in a consistent way.
 * Matches the Python format_datetime function.
 */
export const formatDateTime = (dt: string): string => {
  return dayjs(dt).utc().format("YYYY-MM-DD HH:mm UTC");
};

/**
 * Get medal emoji for rank
 */
export const getMedalEmoji = (rank: number): string => {
  switch (rank) {
    case 1:
      return "🥇";
    case 2:
      return "🥈";
    case 3:
      return "🥉";
    default:
      return "";
  }
};
