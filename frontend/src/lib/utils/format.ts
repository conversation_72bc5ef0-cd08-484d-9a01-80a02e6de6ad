import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";

dayjs.extend(utc);

/**
 * Format datetime in a consistent way.
 * Matches the Python format_datetime function.
 */
export const formatDateTime = (dt: string): string => {
  return dayjs(dt).utc().format("YYYY-MM-DD HH:mm UTC");
};

/**
 * Get medal emoji for rank
 */
export const getMedalEmoji = (rank: number): string => {
  switch (rank) {
    case 1:
      return "🥇";
    case 2:
      return "🥈";
    case 3:
      return "🥉";
    default:
      return "";
  }
};
