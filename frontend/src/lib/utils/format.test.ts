import { formatUs, formatDateTime, getMedalEmoji } from "./format";
import { toTimeLeft } from "../date/utils";

describe("format utilities", () => {
  describe("getMedalEmoji", () => {
    it("returns correct medal emojis", () => {
      expect(getMedalEmoji(1)).toBe("🥇");
      expect(getMedalEmoji(2)).toBe("🥈");
      expect(getMedalEmoji(3)).toBe("🥉");
      expect(getMedalEmoji(4)).toBe("");
      expect(getMedalEmoji(0)).toBe("");
    });
  });

  describe("toTimeLeft", () => {
    it("returns 'ended' for past dates", () => {
      const pastDate = "2020-01-01T00:00:00Z";
      expect(toTimeLeft(pastDate)).toBe("ended");
    });

    it("calculates time remaining for future dates", () => {
      // This test is a bit tricky since it depends on current time
      // We'll just check that it returns a string with expected format
      const futureDate = "2030-12-31T23:59:59Z";
      const result = toTimeLeft(futureDate);
      expect(result).toMatch(/\d+ days? \d+ hours? remaining/);
    });
  });

  describe("formatDateTime", () => {
    it("formats datetime correctly", () => {
      const testDate = "2025-03-24T12:00:00Z";
      expect(formatDateTime(testDate)).toBe("2025-03-24 12:00 UTC");
    });
  });
});
